export type newsType = {
	title: string;
	url: string;
	publishedAt: Date;
};

export const newsAll: newsType[] = [
	// {
	// 	title: "YouStylize Transforms Photos Into Magical Art with AI, Now Listed on Crunchbase",
	// 	url: "https://www.crunchbase.com/organization/youstylize",
	// 	publishedAt: new Date("2025-05-26"),
	// },
	// {
	// 	title: "Unoscribe now can be found on Webwiki",
	// 	url: "https://www.webwiki.com/unoscribe.com",
	// 	publishedAt: new Date("2025-07-11"),
	// },
	// {
	// 	title: "Unoscribe now shared on Pastelink",
	// 	url: "https://pastelink.net/vbky4z0n",
	// 	publishedAt: new Date("2025-07-10"),
	// },
	// {
	// 	title: "Unoscribe now shared on Tumblr",
	// 	url: "https://www.tumblr.com/ielliot/788469974863855616/unoscribe-transcribe-audio-and-video-to-accurate",
	// 	publishedAt: new Date("2025-07-08"),
	// },
	// {
	// 	title: "Unoscribe now can be found on SiteLike",
	// 	url: "https://www.sitelike.org/similar/unoscribe.com",
	// 	publishedAt: new Date("2025-07-07"),
	// },
	// {
	// 	title: "Unoscribe now can be found on Website Carbon Calculator",
	// 	url: "https://www.websitecarbon.com/website/unoscribe-com",
	// 	publishedAt: new Date("2025-07-07"),
	// },
	// {
	// 	title: "Unoscribe now featured on Lovable Launched",
	// 	url: "https://launched.lovable.dev/unoscribe",
	// 	publishedAt: new Date("2025-07-03"),
	// },
	// {
	// 	title: "Unoscribe now can be found on Website Informer",
	// 	url: "https://website.informer.com/unoscribe.com",
	// 	publishedAt: new Date("2025-07-02"),
	// },
];

export type mentionType = {
	name: string;
	url: string;
};
export const mentionsAll: mentionType[] = [
	// {
	// 	name: "Pinterest",
	// 	url: "https://www.pinterest.com/pin/629941066668775020/",
	// },
	// {
	// 	name: "Github",
	// 	url: "https://github.com/elliotodyl",
	// },
	// {
	// 	name: "Behance",
	// 	url: "https://www.behance.net/ielliot",
	// },
	// {
	// 	name: "Linktree",
	// 	url: "https://linktr.ee/ielliot",
	// },
	// {
	// 	name: "solo.to",
	// 	url: "https://solo.to/ielliot",
	// },
	// {
	// 	name: "SoundCloud",
	// 	url: "https://soundcloud.com/theelliot",
	// },
	// {
	// 	name: "lit.link",
	// 	url: "https://lit.link/en/ielliot",
	// },
	// {
	// 	name: "Gravatar",
	// 	url: "https://gravatar.com/supernaturally8ccc69295c",
	// },
	// {
	// 	name: "Vocal",
	// 	url: "https://vocal.media/authors/elliot-g18fs0jwk",
	// },
	// {
	// 	name: "MyAnimeList",
	// 	url: "https://myanimelist.net/profile/theelliot",
	// },
	// {
	// 	name: "AllMyLinks",
	// 	url: "https://allmylinks.com/ielliot",
	// },
	// {
	// 	name: "Onvasortir",
	// 	url: "https://dunkerque.onvasortir.com/profil_read.php?Theelliot",
	// },
	// {
	// 	name: "Bento",
	// 	url: "https://bento.me/ielliot",
	// },
	// {
	// 	name: "Biolinky",
	// 	url: "https://biolinky.co/elliot",
	// },
	// {
	// 	name: "GetAllMyLinks",
	// 	url: "https://getallmylinks.com/elliot",
	// },
	// {
	// 	name: "ShippingExplorer",
	// 	url: "https://www.shippingexplorer.net/en/user/ielliot/166851",
	// },
];
