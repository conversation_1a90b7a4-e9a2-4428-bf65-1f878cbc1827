import { NextResponse } from "next/server";
import { <PERSON>th<PERSON><PERSON><PERSON>, <PERSON>ms<PERSON>rror, Credits402Error, FreeLimitError } from "@/@types/error";

export function handleApiError(error: any, eventName: string) {
	console.error("API error:", error);

	const errorClasses = [AuthError, ParamsError, Credits402Error, FreeLimitError];
	const knownError = errorClasses.find((errorClass) => error instanceof errorClass);

	if (knownError) {
		return NextResponse.json({
			status: error.statusCode,
			message: error.message,
		});
	}

	return NextResponse.json({ status: 500, error: error.message });
}
