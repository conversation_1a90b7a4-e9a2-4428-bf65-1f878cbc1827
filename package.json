{"name": "cropimage", "version": "2025.07.15", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev", "dev:turbopack": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@lucide/lab": "^0.1.2", "@next/third-parties": "^15.3.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.8", "@shadcn/ui": "^0.0.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "docx": "^9.5.1", "embla-carousel-react": "^8.5.2", "i18next": "^23.7.12", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "jose": "^6.0.10", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "motion": "^12.6.3", "nanoid": "^5.1.5", "next": "^15.3.3", "next-client-cookies": "^2.0.1", "next-google-adsense": "^1.0.13", "next-i18n-router": "^5.5.1", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "ofetch": "^1.4.1", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-cookie": "^8.0.1", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-microsoft-clarity": "^2.0.0", "react-use-measure": "^2.1.7", "react-wrap-balancer": "^1.1.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.2", "superjson": "^2.2.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "url-slug": "^4.0.1", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "eslint": "^9.27.0", "eslint-config-next": "15.3.3", "eslint-plugin-react-you-might-not-need-an-effect": "^0.0.43", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.1.6"}}