"use client";

import React, { useState } from "react";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/logo";
import { EMAIL_CONTACT, WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { toast } from "sonner";
import Link from "next/link";

interface MenuItem {
	name: string;
	href?: string;
	prefetch?: boolean;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
	items?: { name: string; href: string; prefetch?: boolean; description?: string; image?: string; next?: boolean }[];
}

const menuItems: MenuItem[] = [
	// { name: "Home", href: "/" },
	// { name: "Blog", href: "/blog", prefetch: false },
];

export const Header = () => {
	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

	return (
		<header className="sticky top-0 z-20 w-full border-b border-transparent bg-white px-2 transition-colors duration-300">
			<div className="flex h-14 flex-wrap items-center justify-between px-4 md:px-6">
				<div className="-ml-4 flex flex-row items-center gap-8 md:-ml-0">
					<Link href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo />
						<span className="text-xl font-[550] text-zinc-900">{WEBNAME}</span>
					</Link>
					<div className="hidden rounded-lg font-normal md:flex md:flex-row">
						<NavigationMenu>
							<NavigationMenuList className="space-x-0">
								{menuItems.map((route, index) => (
									<React.Fragment key={index}>
										{route.items ? (
											<NavigationMenuItem>
												<NavigationMenuTrigger className="bg-transparent px-3 font-normal text-black/70 hover:bg-transparent focus:bg-transparent data-active:bg-transparent data-[state=open]:bg-transparent">
													{route.name}
												</NavigationMenuTrigger>
												<NavigationMenuContent>
													<div className="space-y-2 px-2 py-4">
														<p className="text-muted-foreground pl-3 text-sm">{route.name}</p>
														<div className="flex w-[280px] flex-col gap-0.5">
															{route.items.map((feature, index) => (
																<Link
																	key={index}
																	href={feature.href}
																	prefetch={feature.prefetch ?? true}
																	className="text-muted-foreground flex flex-row items-center rounded-lg px-1 py-0.5 hover:bg-zinc-100"
																>
																	{feature.image && (
																		<img
																			src={feature.image}
																			alt={feature.name}
																			className="aspect-square h-16 rounded-lg object-cover"
																			loading="lazy"
																		/>
																	)}
																	<div className="p-2">
																		<p className="text-sm text-black">{feature.name}</p>
																		{feature.description && (
																			<p className="text-muted-foreground text-xs">{feature.description}</p>
																		)}
																	</div>
																	{feature.next && <ArrowRight className="text-muted-foreground mr-2 ml-auto h-4 w-4" />}
																</Link>
															))}
														</div>
													</div>
												</NavigationMenuContent>
											</NavigationMenuItem>
										) : (
											<NavigationMenuItem>
												<NavigationMenuLink
													className={cn("hover:text-accent-foreground px-3 py-2 text-sm hover:bg-transparent")}
													asChild
												>
													<Link
														href={route.href!}
														prefetch={route.prefetch ?? true}
														className="flex flex-row items-center font-normal text-black/70"
														target={route.target}
													>
														{route.name}
														{route.icon && <>{route.icon}</>}
													</Link>
												</NavigationMenuLink>
											</NavigationMenuItem>
										)}
									</React.Fragment>
								))}
							</NavigationMenuList>
						</NavigationMenu>
					</div>
				</div>

				<div className="flex flex-row items-center gap-3"></div>
			</div>

			<Dialog open={showMobileMenu} onOpenChange={setShowMobileMenu}>
				<DialogContent className="h-[98dvh] w-[98dvw] max-w-full rounded">
					<DialogHeader>
						<DialogTitle className="text-start">
							<Link href="/" className="flex items-center space-x-2">
								<Logo />
								<span className="text-lg font-semibold">{WEBNAME}</span>
							</Link>
						</DialogTitle>
						<div className="h-full pt-3 text-start">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<div className="space-y-2">
											<Accordion type="single" collapsible>
												<AccordionItem value="item-1">
													<AccordionTrigger className="py-3 text-base font-normal text-black/80 hover:no-underline">
														{route.name}
													</AccordionTrigger>
													<AccordionContent className="space-y-2">
														{route.items.map((route, index) => (
															<div key={index} className="">
																<Link
																	href={route.href}
																	prefetch={route.prefetch ?? true}
																	className="flex flex-row items-center text-black/70"
																	onClick={() => setShowMobileMenu(false)}
																>
																	<p className="items-center">{route.name}</p>
																	{route.next && <ArrowRight className="ml-auto h-3.5 w-3.5" />}
																</Link>
															</div>
														))}
													</AccordionContent>
												</AccordionItem>
											</Accordion>
										</div>
									) : (
										<div className="">
											<div className="py-3">
												<Link
													href={route.href!}
													prefetch={route.prefetch ?? true}
													className="font-normal text-black/80"
													target={route.target}
													onClick={() => setShowMobileMenu(false)}
												>
													<p className="items-center">
														{route.name}
														{route.icon && <>{route.icon}</>}
													</p>
												</Link>
											</div>
											<Separator className="" />
										</div>
									)}
								</React.Fragment>
							))}
						</div>
					</DialogHeader>
				</DialogContent>
			</Dialog>
		</header>
	);
};
