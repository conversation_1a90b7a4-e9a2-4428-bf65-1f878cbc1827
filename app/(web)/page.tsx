import { OSS_URL, WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import { AudioLines, Infinity, CheckCircle, Clock, Download, <PERSON><PERSON>he<PERSON>, Zap } from "lucide-react";
import { GridSections } from "@/components/landing/grid-sections";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { HowToUse } from "@/components/landing/how-to-use";
import { BentoItem } from "@/components/landing/features-bento";
import { CropImageClient } from "@/components/app/crop-image.client";
import { Announcement, AnnouncementTitle } from "@/components/ui/kibo-ui/announcement";

export const metadata: Metadata = {
	title: `${WEBNAME} - Transcribe Audio and Video to Accurate Text`,
	description:
		"Transcribe audio and video to text with high accuracy, speed, and speaker recognition. Fast, unlimited audio & video transcription made easy.",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-12 pb-12">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 text-center lg:mt-12">
							<h1 className="mx-auto max-w-3xl text-3xl font-semibold sm:text-4xl">Crop Image</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<p>Crop your image files online for free.</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container w-full px-4 pb-36">
				<CropImageClient />
			</div>
		</main>
	);
}
