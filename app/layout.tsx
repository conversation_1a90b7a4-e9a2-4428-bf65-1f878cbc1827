import "./globals.css";
import { fontHeading, fontSans } from "@/lib/fonts";
import { Toaster } from "@/components/ui/sonner";
import { CookiesProvider } from "next-client-cookies/server";
import NextTopLoader from "nextjs-toploader";
import { Metadata } from "next";
import { WEB_URL, WEBNAME } from "@/lib/constants";
import { AnalyticsClarity } from "@/components/analytics/analytics-clarity";
import { AnalyticsGoolge } from "@/components/analytics/analytics-google";

export const metadata: Metadata = {
	metadataBase: new URL(WEB_URL),
	openGraph: {
		title: `${WEBNAME}`,
		type: "website",
		url: WEB_URL,
	},
	twitter: {
		site: WEB_URL,
	},
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<link rel="icon" href="/favicon.ico" sizes="any" />
			</head>

			<body className={`${fontSans.variable} font-sans ${fontHeading.variable} overscroll-none`}>
				<NextTopLoader color="#3b82f6" initialPosition={0.3} speed={600} crawlSpeed={200} showSpinner={false} shadow={false} />
				<CookiesProvider>
					{children}
					<Toaster richColors position="top-center" />
					{/* <AnalyticsClarity />
					<AnalyticsGoolge /> */}
				</CookiesProvider>
			</body>
		</html>
	);
}
