import { MetadataRoute } from "next";
import { WEB_URL } from "@/lib/constants";

export const dynamic = "force-static";

interface SitemapEntry {
	url: string;
	lastModified?: Date;
	changeFrequency?: "monthly" | "daily" | "always" | "hourly" | "weekly" | "yearly" | "never";
	priority?: number;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = WEB_URL;
	const nowDate = new Date();

	const pages = [].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	const normal = ["/terms-of-use", "/privacy-policy"].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	return [{ url: baseUrl, lastModified: nowDate }, ...pages, ...normal] as SitemapEntry[];
}
