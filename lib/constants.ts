// ======================== config keys ========================
// Analytics
export const MIXPANEL_TOKEN = "";
export const CLARITY_TOKEN = "qb74yecqmp";
export const GA_TOKEN = "G-8KTLJXCEVQ"; // Google Analytics
export const GTM_TOKEN = "GTM-546PSDLP"; // Google Tag Manager

// ======================== utils keys ========================
export const WEB_HOST = "cropimage.cv";
export const WEB_URL = process.env.NODE_ENV === "production" ? `https://${WEB_HOST}` : "http://localhost:3000";
export const WEBNAME = "Crop Image";
export const EMAIL_CONTACT = "<EMAIL>";
export const CALLBACK_URL_TRANSCRIBE_WEBHOOK = process.env.NODE_ENV === "production" ? "https://unoscribe.com" : "https://dev-next.xav.im";

// Auth
export const ROUTE_PATH_SIGN_IN = "/";
export const ROUTE_PATH_SIGN_IN_AFTER = "/";

// OSS
export const CLOUDFLARE_R2_BUCKET_NAME = "unoscribe";
export const OSS_URL = "https://static.unoscribe.com";

// Cookie

// Duration
export const DURATION_1_HOUR = 60 * 60;
export const DURATION_6_HOUR = 6 * DURATION_1_HOUR;
export const DURATION_1_DAY = 24 * DURATION_1_HOUR;
export const DURATION_1_WEEK = 7 * DURATION_1_DAY;
export const DURATION_1_MONTH = 30 * DURATION_1_DAY;
export const DURATION_6_MONTH = 180 * DURATION_1_DAY;

// File Upload limit
export const FILE_SIZE_LIMIT_FREE = 1024 * 1024 * 10; // 100MB
